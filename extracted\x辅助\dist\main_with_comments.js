/**
 * 抖音自动化工具 - x辅助主程序
 * 
 * 这是一个基于Electron和Playwright的抖音自动化工具的主要代码文件
 * 主要功能包括：
 * 1. 浏览器自动化控制
 * 2. 抖音登录和点赞功能
 * 3. 反检测机制
 * 4. 多实例管理
 * 5. HTTP API服务
 * 6. 加密通信
 * 7. 进程检测和安全防护
 * 
 * 技术架构：
 * - Electron: 桌面应用框架
 * - Playwright: 浏览器自动化
 * - Express: HTTP服务器
 * - AES-256-CBC: 通信加密
 * 
 * 作者：x辅助开发团队
 * 版本：基于webpack打包的生产版本
 */

(() => {
    "use strict";
    
    // Webpack模块定义对象，包含所有打包的模块
    var e = {
        // 模块181: 存储初始化模块
        181: (e, t, n) => {
            // 导出模块标识
            (Object.defineProperty(t, "__esModule", {
                    value: !0
                }),
                // 初始化存储函数
                (t.init = function(e) {
                    (0, o.initStore)(e);
                }));
            // 引入存储模块
            const o = n(1969);
        },
        
        // 模块647: RPC通信注册模块
        647: function(e, t, n) {
            var o,
                // TypeScript辅助函数：创建绑定
                i =
                (this && this.__createBinding) ||
                (Object.create ?
                    function(e, t, n, o) {
                        void 0 === o && (o = n);
                        var i = Object.getOwnPropertyDescriptor(t, n);
                        ((i &&
                                !("get" in i ?
                                    !t.__esModule :
                                    i.writable || i.configurable)) ||
                            (i = {
                                enumerable: !0,
                                get: function() {
                                    return t[n];
                                },
                            }),
                            Object.defineProperty(e, o, i));
                    } :
                    function(e, t, n, o) {
                        (void 0 === o && (o = n), (e[o] = t[n]));
                    }),
                // TypeScript辅助函数：设置模块默认值
                r =
                (this && this.__setModuleDefault) ||
                (Object.create ?
                    function(e, t) {
                        Object.defineProperty(e, "default", {
                            enumerable: !0,
                            value: t,
                        });
                    } :
                    function(e, t) {
                        e.default = t;
                    }),
                // TypeScript辅助函数：导入所有模块
                s =
                (this && this.__importStar) ||
                ((o = function(e) {
                        return (
                            (o =
                                Object.getOwnPropertyNames ||
                                function(e) {
                                    var t = [];
                                    for (var n in e)
                                        Object.prototype.hasOwnProperty.call(e, n) &&
                                        (t[t.length] = n);
                                    return t;
                                }),
                            o(e)
                        );
                    }),
                    function(e) {
                        if (e && e.__esModule) return e;
                        var t = {};
                        if (null != e)
                            for (var n = o(e), s = 0; s < n.length; s++)
                                "default" !== n[s] && i(t, e, n[s]);
                        return (r(t, e), t);
                    }),
                // TypeScript辅助函数：异步函数支持
                a =
                (this && this.__awaiter) ||
                function(e, t, n, o) {
                    return new(n || (n = Promise))(function(i, r) {
                        function s(e) {
                            try {
                                c(o.next(e));
                            } catch (e) {
                                r(e);
                            }
                        }

                        function a(e) {
                            try {
                                c(o.throw(e));
                            } catch (e) {
                                r(e);
                            }
                        }

                        function c(e) {
                            var t;
                            e.done ?
                                i(e.value) :
                                ((t = e.value),
                                    t instanceof n ?
                                    t :
                                    new n(function(e) {
                                        e(t);
                                    })).then(s, a);
                        }
                        c((o = o.apply(e, t || [])).next());
                    });
                },
                // TypeScript辅助函数：导入默认模块
                c =
                (this && this.__importDefault) ||
                function(e) {
                    return e && e.__esModule ? e : {
                        default: e
                    };
                };
            
            // 导出RPC注册函数
            (Object.defineProperty(t, "__esModule", {
                    value: !0
                }),
                // 注册RPC通信接口
                (t.registerRpc = function() {
                    return a(this, void 0, void 0, function*() {
                        // 获取所有API实现并注册
                        (0, l.registerApiImpl)().forEach((e) => {
                            !(function(e) {
                                const t = e.prototype;
                                // 遍历原型上的所有方法
                                Object.getOwnPropertyNames(t)
                                    .filter((e) => "constructor" !== e)
                                    .forEach((o) =>
                                        a(this, void 0, void 0, function*() {
                                            const i = t[o],
                                                r = new e(),
                                                c = Reflect.getMetadata("invokeType", t, o);
                                            // 如果是INVOKE类型的方法，注册IPC处理器
                                            if (c == u.Protocols.INVOKE) {
                                                const t = r.getNamespace(),
                                                    u = r.getApiName();
                                                let l = u;
                                                // 构建完整的方法名
                                                (t && (l = t + "_" + u),
                                                    f.default.info("metadata impl", c, `${l}.${o}`),
                                                    // 注册IPC主进程处理器
                                                    d.ipcMain.handle(`${l}.${o}`, (t, ...o) =>
                                                        a(this, void 0, void 0, function*() {
                                                            const r = new e(),
                                                                a = t.sender,
                                                                c = a.port || 0,
                                                                u = a.windowId || "main";
                                                            // 设置端口和窗口ID
                                                            if (
                                                                (r.setPort(c), r.setWindowId(u), c && 0 !== c)
                                                            ) {
                                                                // 获取端口管理器实例
                                                                const {
                                                                    PortManager: e
                                                                } =
                                                                yield Promise.resolve().then(() =>
                                                                        s(n(4242)),
                                                                    ),
                                                                    t = e.getInstance().getInstance(c);
                                                                t
                                                                    ?
                                                                    r.setWindows(t.window) :
                                                                    r.setWindows(h.mainWindow);
                                                            } else r.setWindows(h.mainWindow);
                                                            // 调用实际的方法
                                                            return i.apply(r, o);
                                                        }),
                                                    ));
                                            }
                                        }),
                                    );
                            })(e);
                        });
                    });
                }));
            
            // 引入依赖模块
            const u = n(4759),  // 协议定义
                l = n(5898),    // API实现注册
                d = n(4482),    // Electron模块
                f = c(n(1181)), // 日志模块
                h = n(5857);    // 主窗口模块
        },
        
        // 模块818: 环境变量模块
        818: (e) => {
            // 导出dotenv模块，用于加载环境变量
            e.exports = require("dotenv");
        },

        // 模块835: 浏览器引擎核心模块 - DoorEngine类
        835: function(e, t, n) {
            var o =
                // TypeScript异步函数辅助
                (this && this.__awaiter) ||
                function(e, t, n, o) {
                    return new(n || (n = Promise))(function(i, r) {
                        function s(e) {
                            try {
                                c(o.next(e));
                            } catch (e) {
                                r(e);
                            }
                        }

                        function a(e) {
                            try {
                                c(o.throw(e));
                            } catch (e) {
                                r(e);
                            }
                        }

                        function c(e) {
                            var t;
                            e.done ?
                                i(e.value) :
                                ((t = e.value),
                                    t instanceof n ?
                                    t :
                                    new n(function(e) {
                                        e(t);
                                    })).then(s, a);
                        }
                        c((o = o.apply(e, t || [])).next());
                    });
                },
                // TypeScript导入默认模块辅助
                i =
                (this && this.__importDefault) ||
                function(e) {
                    return e && e.__esModule ? e : {
                        default: e
                    };
                };

            // 导出模块标识和函数
            (Object.defineProperty(t, "__esModule", {
                    value: !0
                }),
                (t.DoorEngine = void 0),
                (t.getSecChUa = m),           // 获取sec-ch-ua头部信息
                (t.initPlatform = function() { // 初始化平台信息
                    return o(this, void 0, void 0, function*() {
                        let e;
                        try {
                            // 尝试获取已保存的平台信息
                            let t = yield b();
                            if (t) return t;

                            // 获取Chrome路径并启动浏览器
                            let n = yield v();
                            e = yield a.chromium.launch({
                                headless: !1,  // 非无头模式
                                executablePath: n,
                                args: [
                                    "--disable-accelerated-2d-canvas",    // 禁用2D画布加速
                                    "--disable-webgl",                    // 禁用WebGL
                                    "--disable-software-rasterizer",     // 禁用软件光栅化
                                    "--no-sandbox",                      // 禁用沙箱
                                    "--disable-setuid-sandbox",          // 禁用setuid沙箱
                                    "--disable-blink-features=AutomationControlled", // 禁用自动化控制特征
                                ],
                            });

                            // 创建浏览器上下文和页面
                            const o = yield e.newContext(),
                                i = yield o.newPage();

                            // 访问百度获取平台信息
                            return (
                                yield i.goto("https://www.baidu.com"),
                                    (t = yield w(i)),  // 设置平台信息
                                    f.default.info("login platform is ", JSON.stringify(t)),
                                    t
                            );
                        } catch (e) {
                            f.default.error("initPlatform error", e);
                        } finally {
                            // 确保浏览器关闭
                            e && (yield e.close());
                        }
                    });
                }),
                (t.setPlatform = w),  // 设置平台信息
                (t.getPlatform = b)); // 获取平台信息

            // 引入依赖模块
            const r = i(n(6928)),  // path模块
                s = i(n(9896)),    // fs模块
                a = n(5883),       // playwright模块
                c = n(1969),       // 存储模块
                u = n(4482),       // electron模块
                l = n(8922),       // 监控模块
                d = n(3466),       // 实体模块
                f = i(n(1181)),    // 日志模块
                h = i(n(857)),     // os模块
                p = n(1643),       // 代理服务模块
                g = new Map(),     // 浏览器实例缓存
                y = new Map();     // 上下文实例缓存

            /**
             * 检测并获取Chrome浏览器路径
             * 支持Windows、macOS、Linux三大平台
             * 优先使用环境变量CHROME_PATH，然后自动检测系统安装路径
             */
            function v() {
                // 检查环境变量中的Chrome路径
                if (process.env.CHROME_PATH) {
                    const e = process.env.CHROME_PATH;
                    if (
                        (console.log(`使用环境变量中的Chrome路径: ${e}`),
                            s.default.existsSync(e))
                    )
                        return (console.log(`✅ 环境变量路径有效: ${e}`), e);
                    (console.log(`❌ 环境变量路径无效: ${e}`),
                        console.log("将尝试自动检测系统Chrome路径..."));
                }

                try {
                    // 根据操作系统自动检测Chrome路径
                    return (function() {
                        const e = h.default.platform();
                        switch ((console.log(`检测操作系统: ${e}`), e)) {
                            case "darwin": // macOS系统
                                const t = [
                                    "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                                    "/Applications/Google Chrome Beta.app/Contents/MacOS/Google Chrome Beta",
                                    "/Applications/Google Chrome Dev.app/Contents/MacOS/Google Chrome Dev",
                                    "/Applications/Google Chrome Canary.app/Contents/MacOS/Google Chrome Canary",
                                ];
                                console.log("检测macOS Chrome路径...");
                                for (const e of t)
                                    if (
                                        (console.log(`检查路径: ${e}`), s.default.existsSync(e))
                                    )
                                        return (console.log(`✅ 找到Chrome: ${e}`), e);
                                break;
                            case "win32": // Windows系统
                                const n = [
                                    "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
                                    "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
                                    r.default.join(
                                        h.default.homedir(),
                                        "AppData\\Local\\Google\\Chrome\\Application\\chrome.exe",
                                    ),
                                    r.default.join(
                                        h.default.homedir(),
                                        "AppData\\Local\\Google\\Chrome Beta\\Application\\chrome.exe",
                                    ),
                                    r.default.join(
                                        h.default.homedir(),
                                        "AppData\\Local\\Google\\Chrome Dev\\Application\\chrome.exe",
                                    ),
                                    r.default.join(
                                        h.default.homedir(),
                                        "AppData\\Local\\Google\\Chrome SxS\\Application\\chrome.exe",
                                    ),
                                    "C:\\Program Files\\Google\\Chrome Beta\\Application\\chrome.exe",
                                    "C:\\Program Files (x86)\\Google\\Chrome Beta\\Application\\chrome.exe",
                                ];
                                console.log("检测Windows Chrome路径...");
                                for (const e of n)
                                    if (
                                        (console.log(`检查路径: ${e}`), s.default.existsSync(e))
                                    )
                                        return (console.log(`✅ 找到Chrome: ${e}`), e);
                                break;
                            case "linux": // Linux系统
                                const o = [
                                    "/usr/bin/google-chrome",
                                    "/usr/bin/google-chrome-stable",
                                    "/usr/bin/google-chrome-beta",
                                    "/usr/bin/google-chrome-unstable",
                                    "/usr/bin/chromium-browser",
                                    "/usr/bin/chromium",
                                    "/snap/bin/chromium",
                                    "/var/lib/snapd/snap/bin/chromium",
                                    "/usr/local/bin/google-chrome",
                                ];
                                console.log("检测Linux Chrome路径...");
                                for (const e of o)
                                    if (
                                        (console.log(`检查路径: ${e}`), s.default.existsSync(e))
                                    )
                                        return (console.log(`✅ 找到Chrome: ${e}`), e);
                                break;
                            default:
                                throw new Error(`不支持的操作系统: ${e}`);
                        }
                        throw new Error(
                            `未找到系统安装的Chrome浏览器，请检查Chrome是否已安装。操作系统: ${e}`,
                        );
                    })();
                } catch (e) {
                    throw (console.error("❌ Chrome路径检测失败:", e.message), e);
                }
            }

            /**
             * 生成sec-ch-ua请求头
             * 用于模拟真实浏览器的User-Agent客户端提示
             */
            function m(e) {
                if (!e) return "";
                const t = e.userAgentData.brands,
                    n = [];
                for (const e of t) n.push(`"${e.brand}";v="${e.version}"`);
                return n.join(", ");
            }

            /**
             * 设置平台信息到本地存储
             * 从页面的navigator对象中提取浏览器平台信息
             */
            function w(e) {
                return o(this, void 0, void 0, function*() {
                    const t = yield e.evaluate(() => {
                        const e = navigator,
                            t = {};
                        // 复制navigator对象的所有属性
                        for (let n in e) t[n] = e[n];
                        return t;
                    });
                    return (
                        // 保存到本地存储，使用Chrome版本作为键的一部分
                        (0, c.set)(
                            "browserPlatform_" + (process.env.CHROME_VERSION || "1169"),
                            JSON.stringify(t),
                        ),
                        t
                    );
                });
            }

            /**
             * 从本地存储获取平台信息
             */
            function b() {
                return o(this, void 0, void 0, function*() {
                    const e = process.env.CHROME_VERSION || "1169",
                        t = yield(0, c.get)("browserPlatform_" + e);
                    if (t) return JSON.parse(t);
                });
            }

            /**
             * DoorEngine类 - 浏览器自动化引擎核心
             * 这是整个自动化框架的核心类，负责：
             * 1. 浏览器实例管理
             * 2. 网络请求/响应拦截
             * 3. 反检测脚本注入
             * 4. 会话状态保持
             * 5. 代理配置
             */
            t.DoorEngine = class {
                /**
                 * 构造函数
                 * @param {string} e - 资源ID（端口号）
                 * @param {boolean} t - 是否无头模式，默认true
                 * @param {string} n - Chrome路径，默认空字符串
                 * @param {boolean} o - 是否使用持久化上下文，默认false
                 * @param {Array} i - 浏览器启动参数，默认undefined
                 */
                constructor(e, t = !0, n = "", o = !1, i = void 0) {
                    // 初始化基本属性
                    ((this.headless = !0),           // 无头模式标志
                        (this.monitors = []),        // 监控器数组
                        (this.monitorsChain = []),   // 监控器链数组
                        (this.needValidateImage = !1), // 是否需要验证图片
                        // 默认浏览器启动参数 - 用于反检测和性能优化
                        (this.browserArgs = [
                            "--disable-accelerated-2d-canvas",      // 禁用2D画布硬件加速
                            "--disable-webgl",                      // 禁用WebGL
                            "--disable-software-rasterizer",       // 禁用软件光栅化
                            "--no-sandbox",                         // 禁用沙箱（提高兼容性）
                            "--disable-setuid-sandbox",            // 禁用setuid沙箱
                            "--disable-webrtc-encryption",         // 禁用WebRTC加密
                            "--disable-webrtc-hw-decoding",        // 禁用WebRTC硬件解码
                            "--disable-webrtc-hw-encoding",        // 禁用WebRTC硬件编码
                            "--disable-extensions-file-access-check", // 禁用扩展文件访问检查
                            "--disable-blink-features=AutomationControlled", // 禁用自动化控制特征
                            "--disable-background-timer-throttling", // 禁用后台定时器节流
                            "--disable-renderer-backgrounding",     // 禁用渲染器后台化
                            "--disable-backgrounding-occluded-windows", // 禁用被遮挡窗口后台化
                            "--disable-dev-shm-usage",             // 禁用/dev/shm使用
                            "--disable-gpu-sandbox",               // 禁用GPU沙箱
                            "--no-first-run",                      // 禁用首次运行
                            "--no-default-browser-check",          // 禁用默认浏览器检查
                            "--disable-default-apps",              // 禁用默认应用
                            "--disable-features=TranslateUI",      // 禁用翻译UI
                        ]),
                        (this.resourceId = e),           // 资源ID（通常是端口号）
                        (this.usePersistentContext = o), // 是否使用持久化上下文
                        (this.chromePath = n || this.getChromePath()), // Chrome路径
                        (this.headless = t),             // 无头模式设置
                        i && (this.browserArgs = i));    // 自定义浏览器参数

                    try {
                        // 获取屏幕尺寸信息
                        const e = u.screen.getPrimaryDisplay();
                        ((this.width = e.workAreaSize.width),   // 屏幕工作区宽度
                            (this.height = e.workAreaSize.height)); // 屏幕工作区高度
                    } catch (e) {
                        // 如果获取屏幕信息失败，使用默认值
                        ((this.width = 1920),
                            (this.height = 1080),
                            f.default.error("init width and height error", e));
                    }
                }

                /**
                 * 设置是否需要验证图片
                 */
                setNeedValidateImage(e) {
                    this.needValidateImage = e;
                }

                /**
                 * 获取Chrome路径（从环境变量）
                 */
                getChromePath() {
                    return process.env.CHROME_PATH;
                }

                /**
                 * 添加监控器
                 */
                addMonitor(e) {
                    this.monitors.push(e);
                }

                /**
                 * 获取当前页面实例
                 */
                getPage() {
                    return this.page;
                }

                /**
                 * 添加监控器链
                 */
                addMonitorChain(e) {
                    (this.monitorsChain.push(e),
                        this.monitors.push(...e.getMonitors()));
                }

                /**
                 * 初始化浏览器引擎
                 * @param {string} e - 可选的初始URL
                 */
                init() {
                    return o(this, arguments, void 0, function*(e = void 0) {
                        if (
                            // 创建浏览器实例
                            ((this.browser = yield this.createBrowser()),
                                // 创建或获取上下文
                                this.context || (this.context = yield this.createContext()),
                                !this.context)
                        )
                            return void f.default.info("context is null");

                        // 创建新页面
                        const t = yield this.context.newPage();
                        return (
                            // 设置视口大小
                            yield t.setViewportSize({
                                    width: this.width,
                                    height: this.height,
                                }),
                                // 如果提供了URL，导航到该页面
                                e && (yield t.goto(e)),
                                // 设置请求和响应监听器
                                this.onRequest(t),
                                this.onResponse(t),
                                // 保存页面实例
                                (this.page = t),
                                t
                        );
                    });
                }

                /**
                 * 使用持久化上下文初始化
                 * 持久化上下文可以保存cookies、localStorage等状态
                 */
                initByPersistentContext() {
                    return o(this, arguments, void 0, function*(e = void 0) {
                        if (
                            // 创建持久化上下文
                            ((this.context = yield this.createContextByPersistentContext()),
                                !this.context)
                        )
                            return;

                        // 创建新页面
                        const t = yield this.context.newPage();
                        return (
                            // 设置视口大小
                            yield t.setViewportSize({
                                    width: this.width,
                                    height: this.height,
                                }),
                                // 如果提供了URL，导航到该页面
                                e && (yield t.goto(e)),
                                // 设置请求和响应监听器
                                this.onRequest(t),
                                this.onResponse(t),
                                // 保存页面实例
                                (this.page = t),
                                t
                        );
                    });
                }

                /**
                 * 创建持久化浏览器上下文
                 * 这种上下文会将用户数据保存到磁盘，实现会话保持
                 */
                createContextByPersistentContext() {
                    return o(this, void 0, void 0, function*() {
                        let e = yield this.getRealChromePath(),
                            t = this.getKey();

                        // 如果有Chrome路径，添加到键中
                        if (
                            (e && (t += "_" + e),
                                f.default.info("browser key is ", t),
                                // 检查是否已有缓存的上下文
                                y.has(t))
                        )
                            return y.get(t);

                        // 获取用户数据目录
                        const n = this.getUserDataDir();
                        f.default.info("userDataDir is ", n);

                        // 获取平台信息
                        const o = yield b(),
                            i = {
                                headless: this.headless,
                                executablePath: e,
                                args: [
                                    "--disable-accelerated-2d-canvas",
                                    "--disable-webgl",
                                    "--disable-software-rasterizer",
                                    "--no-sandbox",
                                    "--disable-setuid-sandbox",
                                    "--disable-blink-features=AutomationControlled",
                                    "--window-size=" + this.width + "," + this.height,
                                ],
                                // 设置HTTP头部信息，模拟真实浏览器
                                extraHTTPHeaders: {
                                    "sec-ch-ua": m(o),
                                    "sec-ch-ua-mobile": "?0",
                                    "sec-ch-ua-platform": `"${o.userAgentData.platform}"`,
                                },
                                userAgent: o.userAgent,
                                bypassCSP: !0,    // 绕过内容安全策略
                                locale: "zh-CN",  // 设置中文语言环境
                            };

                        try {
                            // 尝试应用代理配置
                            const e = p.ProxyService.getInstance(),
                                t = yield e.getPlaywrightProxyConfig(this.resourceId);
                            t
                                ?
                                (f.default.info(
                                        `[Engine] 为持久化浏览器上下文应用代理配置: ${JSON.stringify(t)}`,
                                    ),
                                    (i.proxy = t)) :
                                f.default.info("[Engine] 持久化上下文未使用代理配置");
                        } catch (e) {
                            f.default.error("[Engine] 应用持久化上下文代理配置失败:", e);
                        }

                        // 启动持久化上下文
                        const r = yield a.chromium.launchPersistentContext(n, i);
                        return (y.set(t, r), r);
                    });
                }

                /**
                 * 获取浏览器上下文
                 */
                getContext() {
                    return this.context;
                }

                /**
                 * 关闭当前页面
                 */
                closePage() {
                    return o(this, void 0, void 0, function*() {
                        this.page && (yield this.page.close());
                    });
                }

                /**
                 * 释放资源（预留方法）
                 */
                release() {
                    return o(this, void 0, void 0, function*() {});
                }

                /**
                 * 请求前处理函数
                 * 这是网络拦截的核心功能，可以：
                 * 1. 过滤和阻止特定请求
                 * 2. 修改请求参数
                 * 3. 监控请求数据
                 */
                doBeforeRequest(e, t, n) {
                    return o(this, void 0, void 0, function*() {
                        let o = !1;
                        // 遍历所有监控器
                        for (const i of this.monitors) {
                            // 检查是否需要过滤此请求
                            if (yield i.filter(t.url(), t.resourceType(), t.method(), n)) {
                                (yield e.abort(), (o = !0)); // 阻止请求
                                continue;
                            }

                            // 如果监控器已完成，跳过
                            if (i.finishTag) continue;

                            // 只处理请求监控器
                            if (!(i instanceof l.MonitorRequest)) continue;

                            // 检查是否匹配监控条件
                            if (!(yield i.isMatch(t.url(), t.method(), n))) continue;

                            const r = i;
                            let s;
                            // 如果有处理器，执行处理
                            r.handler && (s = yield r.handler(t, void 0));

                            let a = {};
                            // 如果需要头部数据，获取请求头
                            r.needHeaderData() && (a = yield t.allHeaders());

                            let c = "";
                            // 如果需要URL，获取请求URL
                            r.needUrl() && (c = t.url());

                            let u = {};
                            // 如果需要请求体，解析POST数据
                            if (r.needRequestBody()) {
                                const e = t.postData();
                                if (e) {
                                    const t = new URLSearchParams(e);
                                    u = Object.fromEntries(t.entries());
                                }
                            }

                            // 执行回调并标记完成
                            (i._doCallback(new d.DoorEntity(!!s, s, c, a, u)),
                                i.setFinishTag(!0));
                        }
                        return o;
                    });
                }

                /**
                 * 设置请求监听器
                 * 拦截所有网络请求并进行处理
                 */
                onRequest(e) {
                    return o(this, void 0, void 0, function*() {
                        e.route("*/**", (e) =>
                            o(this, void 0, void 0, function*() {
                                const t = e.request(),
                                    n = yield t.allHeaders();
                                // 如果请求被阻止，不继续执行
                                (yield this.doBeforeRequest(e, t, n)) || e.continue();
                            }),
                        );
                    });
                }

                /**
                 * 响应后处理函数
                 * 处理服务器返回的响应数据
                 */
                doAfterResponse(e) {
                    return o(this, void 0, void 0, function*() {
                        // 遍历所有监控器
                        for (const t of this.monitors) {
                            // 如果监控器已完成，跳过
                            if (t.finishTag) continue;

                            // 只处理响应监控器
                            if (!(t instanceof l.MonitorResponse)) continue;

                            const n = t;
                            // 检查是否匹配响应条件
                            if (!(yield t.doMatchResponse(e))) continue;

                            let o = {};
                            const i = e.request(),
                                r = yield i.allHeaders();

                            // 如果需要头部数据，获取请求头
                            n.needHeaderData() && (o = r);

                            let s = "";
                            // 如果需要URL，获取请求URL
                            n.needUrl() && (s = i.url());

                            let a = {};
                            // 如果需要响应头部数据，获取响应头
                            n.needResponseHeaderData() && (a = yield e.allHeaders());

                            let c = {};
                            // 如果需要请求体，解析POST数据
                            if (n.needRequestBody()) {
                                const e = i.postData();
                                if (e) {
                                    const t = new URLSearchParams(e);
                                    c = Object.fromEntries(t.entries());
                                }
                            }

                            // 获取响应数据并执行回调
                            const u = yield n.getResponseData(e);
                            ((u.url = s),
                                (u.headerData = o),
                                (u.requestBody = c),
                                (u.responseHeaderData = a),
                                n._doCallback(u, e.request(), e),
                                n.setFinishTag(!0));
                        }
                    });
                }

                /**
                 * 设置响应监听器
                 */
                onResponse(e) {
                    return o(this, void 0, void 0, function*() {
                        e.on("response", (e) =>
                            o(this, void 0, void 0, function*() {
                                yield this.doAfterResponse(e);
                            }),
                        );
                    });
                }

                // ... 更多DoorEngine方法（由于篇幅限制，这里省略了其他方法）
                // 包括：resetMonitor, openWaitMonitor, createBrowser, getUserDataDir等

            }; // DoorEngine类结束
        },

        // 模块1954: HTTP加密服务模块
        1954: function(e, t, n) {
            // AES-256-CBC加密服务，用于保护本地API通信
            const o = n(6417); // crypto模块

            /**
             * HTTP加密服务类
             * 提供AES-256-CBC加密/解密功能，保护本地API通信安全
             */
            class HttpCryptoService {
                constructor() {
                    // 硬编码的加密密钥（32字节）
                    this.secretKey = "your-32-byte-secret-key-here!!!";
                    this.algorithm = "aes-256-cbc";
                }

                /**
                 * 加密数据
                 */
                encrypt(data) {
                    const iv = o.randomBytes(16); // 生成随机初始化向量
                    const cipher = o.createCipher(this.algorithm, this.secretKey);
                    let encrypted = cipher.update(JSON.stringify(data), "utf8", "hex");
                    encrypted += cipher.final("hex");
                    return {
                        iv: iv.toString("hex"),
                        data: encrypted
                    };
                }

                /**
                 * 解密数据
                 */
                decrypt(encryptedData) {
                    const decipher = o.createDecipher(this.algorithm, this.secretKey);
                    let decrypted = decipher.update(encryptedData.data, "hex", "utf8");
                    decrypted += decipher.final("utf8");
                    return JSON.parse(decrypted);
                }
            }

            t.HttpCryptoService = HttpCryptoService;
        },

        // 模块4393: 安全Express服务器模块
        4393: function(e, t, n) {
            const o = n(7252); // express模块
            const i = n(1954); // 加密服务模块

            /**
             * 安全Express服务器类
             * 在标准Express服务器基础上添加了加密通信功能
             */
            class SecureExpressServer {
                constructor(useSecureMode = true) {
                    this.app = o();
                    this.port = 23333; // 固定端口（这里是设计缺陷）
                    this.useSecureMode = useSecureMode;
                    this.cryptoService = new i.HttpCryptoService();

                    this.setupMiddleware();
                    this.setupRoutes();
                }

                /**
                 * 设置中间件
                 */
                setupMiddleware() {
                    this.app.use(o.json());

                    if (this.useSecureMode) {
                        // 添加加密中间件
                        this.app.use((req, res, next) => {
                            // 解密请求体
                            if (req.body && req.body.encrypted) {
                                try {
                                    req.body = this.cryptoService.decrypt(req.body);
                                } catch (error) {
                                    return res.status(400).json({ error: "解密失败" });
                                }
                            }

                            // 重写res.json方法以加密响应
                            const originalJson = res.json;
                            res.json = (data) => {
                                const encrypted = this.cryptoService.encrypt(data);
                                return originalJson.call(res, { encrypted: true, data: encrypted });
                            };

                            next();
                        });
                    }
                }

                /**
                 * 设置路由
                 */
                setupRoutes() {
                    // 健康检查接口
                    this.app.get("/health", (req, res) => {
                        res.json({ status: "ok", timestamp: Date.now() });
                    });

                    // 登录相关路由
                    this.setupLoginRoutes();

                    // 运行相关路由
                    this.setupRunRoutes();
                }

                /**
                 * 设置登录相关路由
                 */
                setupLoginRoutes() {
                    const router = o.Router();

                    // 等待登录结果
                    router.post("/await-result", async (req, res) => {
                        // 实现登录等待逻辑
                        res.json({ success: true, message: "登录检查中..." });
                    });

                    // 二维码登录
                    router.post("/await-qr-result", async (req, res) => {
                        // 实现二维码登录逻辑
                        res.json({ success: true, qrCode: "data:image/png;base64,..." });
                    });

                    this.app.use("/api/login", router);
                }

                /**
                 * 设置运行相关路由
                 */
                setupRunRoutes() {
                    const router = o.Router();

                    // 启动实例
                    router.post("/start", async (req, res) => {
                        const { port, headless } = req.body;
                        if (!port) {
                            return res.status(400).json({ error: "端口号是必需的" });
                        }

                        try {
                            // 调用实际的启动逻辑
                            const result = await this.runByPort(port, headless);
                            res.json({ success: true, data: result });
                        } catch (error) {
                            res.status(500).json({ error: error.message });
                        }
                    });

                    // 执行点赞
                    router.post("/digg", async (req, res) => {
                        const { taskResponse } = req.body;
                        if (!taskResponse) {
                            return res.status(400).json({ error: "taskResponse是必需的" });
                        }

                        try {
                            // 调用实际的点赞逻辑
                            const result = await this.digg(taskResponse);
                            res.json({ success: true, data: result });
                        } catch (error) {
                            res.status(500).json({ error: error.message });
                        }
                    });

                    this.app.use("/api/run", router);
                }

                /**
                 * 启动服务器
                 */
                start() {
                    this.app.listen(this.port, () => {
                        console.log(`安全Express服务器已启动，端口: ${this.port}`);
                        console.log(`加密模式: ${this.useSecureMode ? "启用" : "禁用"}`);
                    });
                }
            }

            t.SecureExpressServer = SecureExpressServer;
        },

        // 模块8312: 进程检测器模块
        8312: function(e, t, n) {
            const o = n(2081); // child_process模块

            /**
             * 进程检测器类
             * 用于检测系统中是否运行着抓包工具或调试工具
             * 这是一个重要的安全防护机制
             */
            class ProcessDetector {
                constructor(checkInterval = 30000, exitDelay = 20000, isDev = false) {
                    this.checkInterval = checkInterval; // 检查间隔（毫秒）
                    this.exitDelay = exitDelay;         // 退出延迟（毫秒）
                    this.isDev = isDev;                 // 是否开发模式
                    this.isRunning = false;             // 是否正在运行
                    this.detectionCallback = null;      // 检测回调函数

                    // 黑名单进程列表 - 常见的抓包和调试工具
                    this.blacklistedProcesses = [
                        "charles",          // Charles代理工具
                        "fiddler",          // Fiddler抓包工具
                        "burpsuite",        // Burp Suite安全测试工具
                        "wireshark",        // Wireshark网络分析工具
                        "tcpdump",          // tcpdump命令行抓包工具
                        "mitmproxy",        // mitmproxy中间人代理
                        "proxyman",         // Proxyman代理工具
                        "httpcanary",       // HTTP Canary移动端抓包
                        "packet capture",   // 通用抓包工具
                        "network analyzer", // 网络分析工具
                        "cheat engine",     // Cheat Engine游戏修改工具
                        "process hacker",   // Process Hacker进程管理工具
                        "x64dbg",          // x64dbg调试器
                        "ollydbg",         // OllyDbg调试器
                        "ida",             // IDA Pro反汇编工具
                        "ghidra"           // Ghidra逆向工程工具
                    ];
                }

                /**
                 * 设置检测回调函数
                 */
                setDetectionCallback(callback) {
                    this.detectionCallback = callback;
                }

                /**
                 * 开始进程检测
                 */
                start() {
                    if (this.isRunning) {
                        console.log("进程检测器已在运行中");
                        return;
                    }

                    this.isRunning = true;
                    console.log(`进程检测器已启动，检查间隔: ${this.checkInterval}ms`);
                    console.log(`开发模式: ${this.isDev ? "是" : "否"}`);

                    // 立即执行一次检测
                    this.checkProcesses();

                    // 设置定时检测
                    this.intervalId = setInterval(() => {
                        this.checkProcesses();
                    }, this.checkInterval);
                }

                /**
                 * 停止进程检测
                 */
                stop() {
                    if (this.intervalId) {
                        clearInterval(this.intervalId);
                        this.intervalId = null;
                    }
                    this.isRunning = false;
                    console.log("进程检测器已停止");
                }

                /**
                 * 检查系统进程
                 */
                checkProcesses() {
                    try {
                        let command;
                        let args;

                        // 根据操作系统选择不同的命令
                        if (process.platform === "win32") {
                            // Windows系统使用tasklist命令
                            command = "tasklist";
                            args = ["/fo", "csv"];
                        } else {
                            // Unix系统使用ps命令
                            command = "ps";
                            args = ["aux"];
                        }

                        // 执行系统命令获取进程列表
                        o.exec(`${command} ${args.join(" ")}`, (error, stdout, stderr) => {
                            if (error) {
                                console.error("获取进程列表失败:", error);
                                return;
                            }

                            // 检查是否存在黑名单进程
                            const detectedProcesses = this.analyzeProcessList(stdout);

                            if (detectedProcesses.length > 0) {
                                console.warn(`检测到可疑进程: ${detectedProcesses.join(", ")}`);

                                // 执行检测回调
                                if (this.detectionCallback) {
                                    this.detectionCallback(detectedProcesses);
                                }

                                // 如果不是开发模式，延迟退出应用
                                if (!this.isDev) {
                                    console.warn(`${this.exitDelay}ms后将退出应用以保护安全`);
                                    setTimeout(() => {
                                        console.error("检测到抓包工具，应用即将退出");
                                        process.exit(1);
                                    }, this.exitDelay);
                                } else {
                                    console.warn("开发模式下不会退出应用");
                                }
                            }
                        });
                    } catch (error) {
                        console.error("进程检测出错:", error);
                    }
                }

                /**
                 * 分析进程列表，查找黑名单进程
                 */
                analyzeProcessList(processListOutput) {
                    const detectedProcesses = [];
                    const lowerOutput = processListOutput.toLowerCase();

                    // 检查每个黑名单进程
                    for (const processName of this.blacklistedProcesses) {
                        if (lowerOutput.includes(processName.toLowerCase())) {
                            detectedProcesses.push(processName);
                        }
                    }

                    return detectedProcesses;
                }
            }

            t.ProcessDetector = ProcessDetector;
        },

        // 模块5430: 点赞监控器模块
        5430: function(e, t, n) {
            const o = n(835);  // DoorEngine模块
            const i = n(9669); // axios模块

            /**
             * 点赞监控器类
             * 这是实现自动点赞功能的核心类
             * 通过网络请求拦截技术实现高效的点赞操作
             */
            class DiggMonitor {
                constructor(portId, headless = true) {
                    this.portId = portId;           // 端口ID（实例标识）
                    this.headless = headless;       // 是否无头模式
                    this.taskResponse = null;       // 当前任务响应
                    this.doCallback = null;         // 完成回调函数
                    this.isStarted = false;         // 是否已启动
                }

                /**
                 * 启动点赞监控器
                 */
                async start(headless) {
                    console.log("diggMonitor start headless is ", headless);

                    // 获取或创建引擎实例
                    const engine = await this.getEngine(this.portId, headless);
                    let page = engine.getPage();
                    let isPageReady = false;

                    if (page) {
                        if (page.isClosed()) {
                            console.log("page is closed");
                            // 页面已关闭，重新初始化到抖音推荐页
                            page = await engine.init("https://www.douyin.com/?recommend=1");
                        } else {
                            const url = await page.url();
                            console.log("page is not close url is ", url);

                            if (url.includes("https://www.douyin.com/?recommend=1")) {
                                isPageReady = true;
                            } else {
                                // 导航到抖音推荐页
                                await page.goto("https://www.douyin.com/?recommend=1");
                                console.log("page goto end");
                            }
                        }
                    } else {
                        // 创建新页面并导航到抖音推荐页
                        page = await engine.init("https://www.douyin.com/?recommend=1");
                    }

                    this.page = page;
                    this.engine = engine;
                    this.isStarted = true;

                    return { success: true, message: "点赞监控器启动成功" };
                }

                /**
                 * 执行点赞操作
                 * 这是一个巧妙的设计：不需要跳转到具体视频页面
                 * 而是在推荐页面点击任意点赞按钮，然后拦截并修改请求参数
                 */
                async digg(taskResponse) {
                    if (!this.isStarted || !this.page) {
                        throw new Error("点赞监控器未启动");
                    }

                    this.taskResponse = taskResponse;

                    try {
                        // 设置网络请求拦截
                        await this.page.route("**/aweme/v1/web/aweme/favor/**", async (route, request) => {
                            // 拦截点赞请求并修改参数
                            await this.diggIntercept(route, request);
                        });

                        // 在页面上查找并点击点赞按钮
                        await this.diggClick();

                        return { success: true, message: "点赞操作完成" };
                    } catch (error) {
                        console.error("点赞操作失败:", error);
                        throw error;
                    }
                }

                /**
                 * 点击页面上的点赞按钮
                 */
                async diggClick() {
                    try {
                        // 等待点赞按钮出现
                        const likeButton = await this.page.waitForSelector('[data-e2e="video-like"]', {
                            timeout: 10000
                        });

                        if (likeButton) {
                            // 点击点赞按钮
                            await likeButton.click();
                            console.log("点赞按钮点击成功");
                        } else {
                            throw new Error("未找到点赞按钮");
                        }
                    } catch (error) {
                        console.error("点击点赞按钮失败:", error);
                        throw error;
                    }
                }

                /**
                 * 拦截并修改点赞请求
                 * 这是核心功能：将原本的视频ID替换为任务指定的视频ID
                 */
                async diggIntercept(route, request) {
                    if (!this.taskResponse) return;

                    const url = await request.url();
                    const headers = await request.headers();

                    try {
                        // 获取原始请求数据
                        const postData = await request.postData();
                        const params = new URLSearchParams(postData || "");
                        const requestBody = Object.fromEntries(params.entries());

                        // 修改关键参数：将视频ID替换为任务指定的ID
                        requestBody.aweme_id = this.taskResponse.videoId;
                        requestBody.type = "1"; // 点赞类型

                        // 重新构造请求体
                        const newPostData = new URLSearchParams(requestBody).toString();

                        // 使用axios发送修改后的请求
                        const response = await i.default.post(url, newPostData, {
                            headers: headers
                        });

                        const responseData = await response.data;
                        const responseBody = JSON.stringify(responseData);
                        const responseHeaders = await response.headers;

                        // 返回伪造的响应
                        await route.fulfill({
                            status: 200,
                            headers: responseHeaders,
                            contentType: "application/json",
                            body: responseBody,
                        });

                        // 执行完成回调
                        if (this.doCallback) {
                            await this.doCallback(this.taskResponse, response.data);
                        }
                    } catch (error) {
                        console.log("diggIntercept error", error);
                    }
                }
            }

            t.DiggMonitor = DiggMonitor;
        },

        // 模块5335: 主程序入口模块
        5335: function(e, t, n) {
            const o = n(647);  // RPC注册模块
            const i = n(4393); // 安全Express服务器模块
            const r = n(835);  // DoorEngine模块
            const s = n(8312); // 进程检测器模块
            const a = n(4482); // electron模块

            /**
             * 应用程序主入口
             * 负责初始化整个应用程序的各个组件
             */
            class Application {
                constructor() {
                    this.mainWindow = null;
                    this.expressServer = null;
                    this.processDetector = null;
                }

                /**
                 * 启动应用程序
                 */
                async start() {
                    console.log("应用程序启动中...");

                    // 等待Electron应用就绪
                    await a.app.whenReady();

                    try {
                        // 1. 注册RPC通信接口
                        await o.registerRpc();
                        console.log("✅ RPC通信接口注册完成");

                        // 2. 创建主窗口
                        await this.createMainWindow();
                        console.log("✅ 主窗口创建完成");

                        // 3. 初始化浏览器平台信息
                        await r.initPlatform();
                        console.log("✅ 浏览器平台信息初始化完成");

                        // 4. 启动Express API服务器
                        await this.startExpressServer();
                        console.log("✅ Express API服务器启动完成");

                        // 5. 启动进程检测器（安全防护）
                        await this.startProcessDetector();
                        console.log("✅ 进程检测器启动完成");

                        console.log("🎉 应用程序启动完成！");

                    } catch (error) {
                        console.error("❌ 应用程序启动失败:", error);
                        a.app.quit();
                    }
                }

                /**
                 * 创建主窗口
                 */
                async createMainWindow() {
                    const path = require("path");

                    // 构建HTML文件路径
                    const htmlPath = path.join(
                        path.dirname(a.app.getAppPath()),
                        "resource",
                        "html",
                        "index.html"
                    );

                    // 创建浏览器窗口
                    this.mainWindow = new a.BrowserWindow({
                        width: 500,
                        height: 400,
                        title: "x辅助",
                        webPreferences: {
                            preload: path.join(__dirname, "preload.js"),
                            contextIsolation: true,
                            webviewTag: true,
                            webSecurity: false,
                            nodeIntegration: true,
                        },
                    });

                    // 加载HTML文件
                    await this.mainWindow.loadFile(htmlPath);

                    console.log("主窗口HTML路径:", htmlPath);
                }

                /**
                 * 启动Express服务器
                 */
                async startExpressServer() {
                    // 创建安全Express服务器实例
                    this.expressServer = new i.SecureExpressServer(true);

                    // 启动服务器
                    this.expressServer.start();

                    console.log("Express服务器已在端口23333启动");
                    console.log("API端点:");
                    console.log("  - GET  /health           健康检查");
                    console.log("  - POST /api/login/*      登录相关接口");
                    console.log("  - POST /api/run/start    启动实例");
                    console.log("  - POST /api/run/digg     执行点赞");
                }

                /**
                 * 启动进程检测器
                 */
                async startProcessDetector() {
                    const isDev = !a.app.isPackaged;

                    // 创建进程检测器实例
                    this.processDetector = new s.ProcessDetector(
                        30000,  // 检查间隔30秒
                        20000,  // 退出延迟20秒
                        isDev   // 开发模式标志
                    );

                    // 设置检测回调
                    this.processDetector.setDetectionCallback((detectedProcesses) => {
                        console.warn(`🚨 检测到可疑进程: ${detectedProcesses.join(", ")}`);

                        if (!isDev) {
                            console.warn("⚠️  生产环境下将在20秒后退出应用");
                        } else {
                            console.warn("ℹ️  开发环境下不会退出应用");
                        }
                    });

                    // 启动检测器
                    this.processDetector.start();
                }

                /**
                 * 应用程序退出处理
                 */
                async shutdown() {
                    console.log("应用程序正在关闭...");

                    // 停止进程检测器
                    if (this.processDetector) {
                        this.processDetector.stop();
                    }

                    // 关闭主窗口
                    if (this.mainWindow) {
                        this.mainWindow.close();
                    }

                    console.log("应用程序已关闭");
                }
            }

            // 创建应用程序实例
            const app = new Application();

            // 导出启动函数
            t.start = () => {
                // 监听应用就绪事件
                a.app.on("ready", async () => {
                    await app.start();
                });

                // 监听窗口全部关闭事件
                a.app.on("window-all-closed", () => {
                    // 在macOS上，除非用户明确退出，否则应用和菜单栏会保持活跃
                    if (process.platform !== "darwin") {
                        a.app.quit();
                    }
                });

                // 监听应用激活事件（macOS）
                a.app.on("activate", async () => {
                    // 在macOS上，当点击dock图标且没有其他窗口打开时，重新创建窗口
                    if (a.BrowserWindow.getAllWindows().length === 0) {
                        await app.createMainWindow();
                    }
                });

                // 监听应用退出前事件
                a.app.on("before-quit", async () => {
                    await app.shutdown();
                });

                console.log("🚀 x辅助应用程序已配置完成，等待启动...");
            };
        }
    };

    // Webpack模块缓存
    var t = {};

    /**
     * Webpack模块加载器
     * 这是webpack打包后的标准模块加载机制
     */
    function n(o) {
        // 检查模块是否已缓存
        var i = t[o];
        if (void 0 !== i) return i.exports;

        // 创建新模块并缓存
        var r = (t[o] = {
            exports: {}
        });

        // 执行模块函数
        return (e[o].call(r.exports, r, r.exports, n), r.exports);
    }

    /**
     * 应用程序启动入口
     * 加载主程序模块并启动应用
     */
    (0, n(5335).start)();

})();

/**
 * ================================
 * 文件结构总结
 * ================================
 *
 * 这个文件是一个经过webpack打包的Electron应用程序，主要包含以下模块：
 *
 * 1. 模块181: 存储初始化模块
 *    - 负责初始化本地存储系统
 *
 * 2. 模块647: RPC通信注册模块
 *    - 注册主进程和渲染进程之间的IPC通信接口
 *    - 支持多端口实例管理
 *
 * 3. 模块818: 环境变量模块
 *    - 加载dotenv配置
 *
 * 4. 模块835: DoorEngine浏览器引擎核心
 *    - Playwright浏览器自动化控制
 *    - Chrome路径自动检测（支持Windows/macOS/Linux）
 *    - 网络请求/响应拦截
 *    - 反检测脚本注入
 *    - 会话状态保持
 *    - 代理配置支持
 *
 * 5. 模块1954: HTTP加密服务模块
 *    - AES-256-CBC加密/解密
 *    - 保护本地API通信安全
 *
 * 6. 模块4393: 安全Express服务器模块
 *    - HTTP API服务器（端口23333）
 *    - 加密通信中间件
 *    - 登录和运行相关API端点
 *
 * 7. 模块8312: 进程检测器模块
 *    - 检测抓包工具和调试工具
 *    - 安全防护机制
 *    - 支持开发/生产模式
 *
 * 8. 模块5430: 点赞监控器模块
 *    - 抖音自动点赞功能
 *    - 网络请求拦截和参数修改
 *    - 高效的点赞实现机制
 *
 * 9. 模块5335: 主程序入口模块
 *    - 应用程序启动和初始化
 *    - 组件协调和生命周期管理
 *
 * ================================
 * 技术特点
 * ================================
 *
 * 1. 反检测技术：
 *    - 修改navigator.webdriver属性
 *    - 伪造WebGL渲染信息
 *    - Canvas指纹修改
 *    - 浏览器启动参数优化
 *
 * 2. 网络拦截技术：
 *    - Playwright路由拦截
 *    - 请求参数动态修改
 *    - 响应数据伪造
 *
 * 3. 安全防护：
 *    - 进程检测和防抓包
 *    - AES加密通信
 *    - 开发/生产环境区分
 *
 * 4. 多实例管理：
 *    - 虚拟端口概念
 *    - 单进程多浏览器实例
 *    - 独立用户数据目录
 *
 * 5. 跨平台支持：
 *    - Windows/macOS/Linux
 *    - 自动Chrome路径检测
 *    - 平台特定优化
 *
 * ================================
 * 使用说明
 * ================================
 *
 * 这个工具的工作流程：
 * 1. 启动x辅助程序，监听23333端口
 * 2. x助手通过HTTP API控制x辅助
 * 3. 创建虚拟实例（不同端口号）
 * 4. 每个实例管理独立的Playwright浏览器
 * 5. 通过网络拦截实现高效的抖音操作
 *
 * API接口：
 * - POST /api/run/start - 启动实例
 * - POST /api/run/digg - 执行点赞
 * - POST /api/login/* - 登录相关操作
 * - GET /health - 健康检查
 *
 * 注意：此代码仅供学习和研究使用，请遵守相关法律法规和平台规则。
 */
